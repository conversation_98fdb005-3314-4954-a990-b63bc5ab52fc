@extends('layout.app')
@section('head')
<style>
    html body .content .content-wrapper{
        margin: 0.6rem 1.2rem;}
    .card-header1{
        margin-bottom: -10px;}
    .table th, .table td{
        padding: 0.2rem 0.2rem;}
    .table.table-bordered.table-hover>thead>tr>th.text-left{
        background-color: rgb(220,220,220);}
    .table-bordered{
        border:2px solid #e3ebf3;}
    .table-label td{
        padding:5px;}
    .table-label td:nth-child(even){
        padding-right:50px;}
    textarea {
        border: none;
        overflow: auto;
        outline: none;
        padding: 0px;
        color: #373a3c;

        -webkit-box-shadow: none;
        -moz-box-shadow: none;
        box-shadow: none;

        resize: none; /* remove the resize handle on the bottom right */
    }
</style>
@endsection
@section('content')
<div class="card-header1">
    <h4 class="card-title" id="basic-layout-colored-form-control">{{__('admin.title.view_allocation')}}</h4>
</div>
<br>
<form name="form_manual_allocate" id="form_manual_allocate">
    <table class="table-label">
        <tbody>
            @if ($allocation->order_type == "Transfer Order")
                <tr>
                    <td width="100px">{{__('admin.label.order_type')}}</td>
                    <td width="300px">: {{$allocation->order_type}}</td>
                    <td width="100px">{{__('admin.label.ref_num')}}</td>
                    <td width="300px">: {{$allocation->ref_num}}</td>
                    <td width="100px">{{__('admin.label.ref_line')}}</td>
                    <td width="300px">: {{$allocation->ref_line}}</td>
                </tr>
                <tr>
                    <td>{{__('admin.label.from_whse')}}</td>
                    <td>: {{$allocation->whse_num}}</td>
                    <td>{{__('admin.label.to_whse')}}</td>
                    <td>: {{$ref->to_whse}}</td>
                </tr>
                <tr>
                    <td>{{__('admin.label.item_num')}}</td>
                    <td>: {{$allocation->item_num}}</td>
                    <td>{{__('admin.label.item_desc')}}</td>
                    <td width="500px"><textarea type="text" rows=3 readonly style="width: 100%; max-width: 100%;">: {{$allocation->item->item_desc ?? null}}</textarea></td>
                    <!-- <td>: {{$allocation->item->item_desc ?? null}}</td> -->
                    <td>{{__('admin.label.uom')}}</td>
                    <td>: {{$allocation->uom}}</td>
                </tr>
            @elseif ($allocation->order_type == "Customer Order")
                <tr>
                    <td>{{__('admin.label.order_type')}}</td>
                    <td>: {{$allocation->order_type}}</td>
                    <td>{{__('admin.label.ref_num')}}</td>
                    <td>: {{$allocation->ref_num}}</td>
                    <td>{{__('admin.label.ref_line')}}</td>
                    <td>: {{$allocation->ref_line}}</td>
                </tr>
                <tr>
                    <td>{{__('admin.label.whse_num')}}</td>
                    <td>: {{$allocation->whse_num}}</td>
                    <td>{{__('admin.label.cust_num')}}</td>
                    <td>: {{$allocation->cust_num}}</td>
                    <td>{{__('admin.label.cust_name')}}</td>
                    <td>: {{$allocation->customer->cust_name ?? null}}</td>
                </tr>
                <tr>
                    <td>{{__('admin.label.item_num')}}</td>
                    <td>: {{$allocation->item_num}}</td>
                    <td>{{__('admin.label.item_desc')}}</td>
                    <td width="500px"><textarea type="text" rows=3 readonly style="width: 100%; max-width: 100%;">: {{$allocation->item->item_desc ?? null}}</textarea></td>
                    <!-- <td>: {{$allocation->item->item_desc ?? null}}</td> -->
                    <td>{{__('admin.label.uom')}}</td>
                    <td>: {{$allocation->uom}}</td>
                </tr>
            @elseif ($allocation->order_type == "Job Order")
                <tr>
                    <td>{{__('admin.label.order_type')}}</td>
                    <td>: {{$allocation->order_type}}</td>
                    <td>{{__('admin.label.ref_num')}}</td>
                    <td>: {{$allocation->ref_num}} - {{$allocation->suffix}}</td>
                    <td>{{__('admin.label.ref_line')}}</td>
                    <td>: {{$allocation->ref_line}}</td>
                </tr>
                <tr>
                    <td>{{__('admin.label.whse_num')}}</td>
                    <td>: {{$allocation->whse_num}}</td>
                    <td>{{__('admin.label.job_item')}}</td>
                    <td>: {{$ref->job->item_num ?? null}}</td>
                    <td>{{__('admin.label.item_desc')}}</td>
                    <td>: {{$ref->job->item->item_desc ?? null}}</td>
                </tr>
                <tr>
                    <td>{{__('admin.label.item_num')}}</td>
                    <td>: {{$allocation->item_num}}</td>
                    <td>{{__('admin.label.item_desc')}}</td>
                    <td width="500px"><textarea type="text" rows=3 readonly style="width: 100%; max-width: 100%;">: {{$allocation->item->item_desc ?? null}}</textarea></td>
                    <!-- <td>: {{$allocation->item->item_desc ?? null}}</td> -->
                    <td>{{__('admin.label.uom')}}</td>
                    <td>: {{$allocation->uom}}</td>
                </tr>
            @endif
            <tr>
                <td>{{__('admin.label.qty_required')}}</td>
                <td>: {{numberFormatPrecision($allocation->qty_required,$unit_quantity_format,'.','')}}</td>
                <td>{{__('admin.label.total_qty_allocated')}}</td>
                <td class="total-qty-allocated">: {{numberFormatPrecision($allocation->qty_allocated,$total_quantity_format,'.','')}}</td>
                <td>{{__('admin.label.qty_balance')}}</td>
                <td class="qty-balance">: {{numberFormatPrecision($allocation->qty_required - $allocation->qty_allocated,$unit_quantity_format,'.','')}}</td>
            </tr>
        </tbody>
    </table>
    <br>
    <table class="table table-bordered table-hover">
        <thead>
            <tr>
                <th class="text-left">{{__('admin.label.loc_num')}}</th>
                <th class="text-left">{{__('admin.label.lot_num')}}</th>
                <th class="text-left">{{__('admin.label.expiry_date')}}</th>
                <th class="text-left">{{__('admin.label.first_received_date')}}</th>
                <th class="text-left">{{__('admin.label.qty_available')}}</th>
                <th class="text-left">{{__('admin.label.qty_allocated')}}</th>
            </tr>
        </thead>
        <tbody class="text-left">
            @forelse ($allocationload as $allocation_location)
                @if($allocation_location->qty_allocated > 0 || $allocation_location->qty_available > 0)
                <tr>
                    <td>{{$allocation_location->loc_num}}</td>
                    <td>{{$allocation_location->lot_num}}</td>
                    <td>{{$allocation_location->expiry_date}}</td>
                    <td>{{$allocation_location->first_received_date}}</td>
                    <td class="text-right">{{numberFormatPrecision($allocation_location->qty_available + $allocation_location->qty_allocated,$unit_quantity_format,'.','')}}</td>
                    <td>
                        <input type="text" step="any" class="text-right allocate" name="qty_manual_allocated[{{$allocation_location->id}}]" style="width:100%"
                            id="{{$allocation_location->id}}"
                            value="{{numberFormatPrecision($allocation_location->qty_allocated,$unit_quantity_format,'.','')}}"
                            disabled>
                    </td>
                </tr>
                @endif
            @empty
                <tr class="text-center">
                    <td colspan="6">{{ __('error.admin.no_data_available_in_table') }}</td>
                </tr>
            @endforelse
        </tbody>
    </table>
    <br>
    <h6>{{__('admin.label.packing_loc')}}</h6>
    <div class="row">
        <div class="col-md-12">
            <div class="row">
                <div class="col-md-12">
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead>
                                <tr>
                                    <th class="text-left">{{__('admin.label.loc_num')}}</th>
                                    <th class="text-left">{{__('admin.label.lot_num')}}</th>
                                    <th class="text-left">{{__('admin.label.expiry_date')}}</th>
                                    <th class="text-left">{{__('admin.label.first_received_date')}}</th>
                                    <th class="text-left">{{__('admin.label.qty_allocated')}}</th>
                                </tr>
                            </thead>
                            <tbody class="text-left">
                                @forelse ($allocation->allocation_locations()->where('picked','Yes')->get() as $allocation_location)
                                    <tr>
                                        <td>{{$allocation_location->loc_num}}</td>
                                        <td>{{$allocation_location->lot_num}}</td>
                                        <td>{{$allocation_location->expiry_date}}</td>
                                        <td>{{$allocation_location->first_received_date}}</td>
                                        <td>
                                            <input type="text" step="any" class="text-right" name="qty_manual_allocated[{{$allocation_location->id}}]" style="width:100%"
                                                id="{{$allocation_location->id}}"
                                                value="{{ numberFormatPrecision($allocation_location->qty_allocated,$unit_quantity_format) }}"
                                                disabled>
                                        </td>
                                    </tr>
                                @empty
                                    <tr class="text-center">
                                        <td colspan="6">{{ __('error.admin.no_data_available_in_table') }}</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <br>
    <div class="row">
        <div class="col-md-12">
            <div style="text-align:center">
                <a href="{{route('allocation.index',['processed' => request('processed')])}}">
                    <button type="button" class="btn btn-warning mr-1">
                        <i class="icon-cross2"></i> {{__('admin.button.cancel')}}
                    </button>
                </a>
            </div>
        </div>
    </div>
</form>
@endsection
